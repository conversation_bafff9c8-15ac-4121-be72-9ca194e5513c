import { useState, useEffect } from "react"
import { X, Filter } from "lucide-react"
import "./sellzio-styles.css"

interface FacetData {
  categories: Record<string, number>
  priceRanges: Record<string, number>
  ratings: Record<string, number>
  shipping: Record<string, number>
  features: Record<string, number>
  provinces: Record<string, number>
}

interface ActiveFilters {
  categories?: string[]
  priceRanges?: string[]
  ratings?: string[]
  shipping?: string[]
  features?: string[]
  // Tambahkan properti untuk facet filter
  kategori?: string[]
  'rentang harga'?: string[]
  rating?: string[]
  pengiriman?: string[]
  fitur?: string[]
  provinsi?: string[]
  kota?: string[]
}

interface SellzioFacetProps {
  searchResults: any[]
  displayedProducts?: any[] // Add this for calculating facet data from displayed products
  activeFilters: ActiveFilters
  onFiltersChange: (filters: ActiveFilters) => void
  isVisible: boolean
  onClose: () => void
  isDesktopSidebar?: boolean
  allProducts?: any[] // Add this to access all products for counting
  subcategoryContext?: {
    category: string
    selectedSubcategory: string
    allSubcategories: Array<{
      id: string
      name: string
      icon?: string
      color?: string
    }>
  } | null
}

export function SellzioFacet({
  searchResults,
  displayedProducts,
  activeFilters,
  onFiltersChange,
  isVisible,
  onClose,
  isDesktopSidebar = false,
  allProducts = [],
  subcategoryContext
}: SellzioFacetProps) {
  const [tempFilters, setTempFilters] = useState<ActiveFilters>(activeFilters)
  const [facetData, setFacetData] = useState<FacetData>({
    categories: {},
    priceRanges: {},
    ratings: {},
    shipping: {},
    features: {},
    provinces: {}
  })
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)
  const [isSubcategoriesExpanded, setIsSubcategoriesExpanded] = useState(false)
  const [isCitiesExpanded, setIsCitiesExpanded] = useState(false)
  const [expandedProvinces, setExpandedProvinces] = useState<Record<string, boolean>>({})
  const [sortedSubcategories, setSortedSubcategories] = useState<string[]>([])


  // Check screen size
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth
      setIsMobile(width < 768) // Mobile: < 768px
      setIsTablet(width >= 768 && width < 1025) // Tablet: 768px - 1024px
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // Extract facets from search results
  useEffect(() => {
    // Use displayedProducts if available, otherwise fallback to searchResults
    const productsForFacets = displayedProducts || searchResults;

    const facets = extractFacets(productsForFacets)
    setFacetData(facets)
  }, [searchResults, displayedProducts, subcategoryContext, tempFilters])

  // Reset temp filters when activeFilters change
  useEffect(() => {
    console.log('🔄 FACET: activeFilters changed, updating tempFilters:', activeFilters);
    setTempFilters({ ...activeFilters });
  }, [activeFilters])

  // Track previous subcategory to detect changes
  const [prevSubcategory, setPrevSubcategory] = useState<string | null>(null);

  // Initialize sorted subcategories when context changes (from subcategory view clicks)
  useEffect(() => {
    const context = subcategoryContext || (window as any).subcategoryContext;
    if (context && context.allSubcategories) {
      const subcategoryNames = context.allSubcategories.map((sub: any) => sub.name);

      // Set selected subcategory if available (from subcategory view click)
      if (context.selectedSubcategory) {
        // Move selected to top ONLY when coming from subcategory view
        const reordered = [
          context.selectedSubcategory,
          ...subcategoryNames.filter((name: string) => name !== context.selectedSubcategory)
        ];
        setSortedSubcategories(reordered);
      } else {
        // No selection from subcategory view, use original order
        setSortedSubcategories(subcategoryNames);
      }
    }
  }, [subcategoryContext]);

  // FIXED: Remove auto-reset logic that causes checkbox revert on mobile/tablet
  // This useEffect was causing tempFilters to reset automatically
  // useEffect(() => {
  //   const context = subcategoryContext || (window as any).subcategoryContext;
  //   const currentSubcategory = context?.selectedSubcategory;
  //   const isActiveFiltersEmpty = Object.keys(activeFilters).length === 0;
  //   if (currentSubcategory && currentSubcategory !== prevSubcategory && !isActiveFiltersEmpty) {
  //     const newFilters = { kategori: [currentSubcategory] };
  //     setTempFilters(newFilters);
  //     setPrevSubcategory(currentSubcategory);
  //   } else if (isActiveFiltersEmpty) {
  //     setPrevSubcategory(currentSubcategory);
  //   }
  // }, [subcategoryContext, prevSubcategory, activeFilters])

  // FIXED: Remove auto-apply useEffect that causes checkbox revert
  // This was causing tempFilters changes to auto-apply on mobile/tablet
  // useEffect(() => {
  //   if (isDesktopSidebar && tempFilters.kategori) {
  //     const timeoutId = setTimeout(() => {
  //       onFiltersChange(tempFilters);
  //     }, 100);
  //     return () => clearTimeout(timeoutId);
  //   }
  // }, [tempFilters, isDesktopSidebar, onFiltersChange])

  // Helper function to detect main category from search results
  const detectMainCategoryFromResults = (results: any[]) => {
    if (!results || results.length === 0) return null;

    // Category mappings with subcategories
    const categoryMappings = {
      'Elektronik': [
        'Konsol Game', 'Aksesoris Konsol', 'Alat Casing', 'Foot Bath & Spa',
        'Mesin Jahit & Aksesoris', 'Setrika & Mesin Uap', 'Purifier & Humidifier',
        'Perangkat Debu & Peralatan Perawatan Lantai', 'Telepon', 'Mesin Cuci & Pengering',
        'Water Heater', 'Pendingin Ruangan', 'Pengering Sepatu', 'Penghangat Ruangan',
        'TV & Aksesoris', 'Perangkat Dapur', 'Lampu', 'Kamera Keamanan',
        'Video Game', 'Kelastrian', 'Baterai', 'Rokok Elektronik & Shisha',
        'Remote Kontrol', 'Walkie Talkie', 'Media Player', 'Perangkat Audio & Speaker',
        'Elektronik Lainnya'
      ],
      'Komputer & Aksesoris': [
        'Laptop', 'Komputer', 'Aksesoris Komputer', 'Software', 'Monitor', 'Keyboard', 'Mouse'
      ],
      'Handphone & Aksesoris': [
        'Handphone', 'Smartphone', 'Aksesoris Handphone', 'Tablet', 'Case Handphone', 'Charger'
      ],
      'Pakaian Pria': [
        'Kemeja', 'Celana', 'Jaket', 'Kaos', 'Pakaian Dalam', 'Sweater', 'Hoodie'
      ],
      'Sepatu Pria': [
        'Sepatu Formal', 'Sepatu Casual', 'Sepatu Olahraga', 'Sandal', 'Boots'
      ],
      'Tas Pria': [
        'Tas Kerja', 'Tas Casual', 'Dompet', 'Ransel', 'Tas Laptop', 'Tas Travel'
      ],
      'Aksesoris Fashion': [
        'Jam Tangan', 'Kacamata', 'Topi', 'Ikat Pinggang', 'Kalung', 'Gelang'
      ],
      'Jam Tangan': [
        'Jam Tangan Pria', 'Jam Tangan Wanita', 'Smartwatch', 'Jam Digital', 'Jam Analog'
      ],
      'Kesehatan': [
        'Vitamin', 'Suplemen', 'Alat Kesehatan', 'Obat-obatan', 'Masker', 'Hand Sanitizer'
      ],
      'Hobi & Koleksi': [
        'Mainan', 'Koleksi', 'Buku', 'Alat Musik', 'Puzzle', 'Board Game'
      ]
    };

    // Count products by category
    const categoryCounts: { [key: string]: number } = {};

    results.forEach(product => {
      // Use the main category directly from product.category
      if (product.category) {
        const mainCategory = product.category;
        categoryCounts[mainCategory] = (categoryCounts[mainCategory] || 0) + 1;
      }
    });

    // Find the main category with most products
    let dominantCategory = null;
    let maxCount = 0;

    for (const [category, count] of Object.entries(categoryCounts)) {
      if (count > maxCount) {
        maxCount = count;
        dominantCategory = category;
      }
    }

    // Return category info if we found a dominant category
    if (dominantCategory && maxCount > 0) {
      return {
        name: dominantCategory,
        subcategories: (categoryMappings as any)[dominantCategory].map((name: string) => ({ name, icon: '📦' }))
      };
    }

    return null;
  };

  const extractFacets = (results: any[]): FacetData => {
    // Use subcategory context from props or window
    const context = subcategoryContext || (window as any).subcategoryContext;

    // ENHANCED: Create context for regular search to enable hierarchical display
    let effectiveContext = context;

    // If no context exists, create one based on search results for hierarchical display
    if (!context && results.length > 0) {
      // Detect main category from search results
      const detectedCategory = detectMainCategoryFromResults(results);
      if (detectedCategory) {
        effectiveContext = {
          category: detectedCategory.name,
          allSubcategories: detectedCategory.subcategories,
          selectedSubcategory: null // No specific subcategory selected in regular search
        };
      }
    }

    // Always show all subcategories if we have context (original or created)
    const shouldShowAllSubcategories = effectiveContext && effectiveContext.allSubcategories;

    // Jika tidak ada hasil atau dalam konteks subkategori, berikan data sample untuk demo
    if (shouldShowAllSubcategories) {
      // If we have subcategory context, use it for categories
      let categories = {
        "Handphone & Tablet": 45,
        "Elektronik": 32,
        "Fashion Pria": 28,
        "Fashion Wanita": 41,
        "Tas & Travel": 19,
        "Sepatu": 23,
        "Aksesoris Fashion": 15
      };

      // Override with subcategory data if available
      if (effectiveContext && effectiveContext.allSubcategories && effectiveContext.allSubcategories.length > 0) {
        const dynamicCategories: { [key: string]: number } = {};

        // Calculate actual product counts for each subcategory based on SMART logic
        let totalCategoryCount = 0;

        // Check if non-category filters are applied
        const hasNonCategoryFilters = (tempFilters['rentang harga']?.length || 0) > 0 ||
                                     (tempFilters.rating?.length || 0) > 0 ||
                                     (tempFilters.pengiriman?.length || 0) > 0 ||
                                     (tempFilters.fitur?.length || 0) > 0;

        effectiveContext.allSubcategories.forEach((sub: any) => {
          // SMART LOGIC: Use filtered products ONLY if non-category filters are applied
          const productsToCount = hasNonCategoryFilters ? results : searchResults;

          const subcategoryCount = productsToCount.filter((product: any) => {
            const subName = sub.name.toLowerCase();
            const productSubcategory = product.subcategory?.toLowerCase() || '';

            // FIXED: Use subcategory field for exact matching
            return productSubcategory === subName;
          }).length;

          // Show all subcategories with their actual product count (even if 0)
          const count = subcategoryCount;
          dynamicCategories[sub.name] = count;
          totalCategoryCount += count;
        });

        // Add main category with total count from all subcategories
        dynamicCategories[effectiveContext.category] = totalCategoryCount;

        categories = dynamicCategories as typeof categories;
      } else {

        // Fallback: Check if we're in Elektronik category based on search results
        const hasElektronikProducts = searchResults.some(product =>
          product.category && product.category.toLowerCase() === 'elektronik'
        );

        if (hasElektronikProducts) {
          const elektronikSubcategories = [
            "Konsol Game", "Aksesoris Konsol", "Alat Casing", "Foot Bath & Spa",
            "Mesin Jahit & Aksesoris", "Setrika & Mesin Uap", "Purifier & Humidifier",
            "Perangkat Debu & Peralatan Perawatan Lantai", "Telepon", "Mesin Cuci & Pengering",
            "Water Heater", "Pendingin Ruangan", "Pengering Sepatu", "Penghangat Ruangan",
            "TV & Aksesoris", "Perangkat Dapur", "Lampu", "Kamera Keamanan",
            "Video Game", "Kelastrian", "Baterai", "Rokok Elektronik & Shisha",
            "Remote Kontrol", "Walkie Talkie", "Media Player", "Perangkat Audio & Speaker",
            "Elektronik Lainnya"
          ];

          const dynamicCategories: { [key: string]: number } = {};

          // Add main category - calculate from actual subcategory counts
          let totalElektronikCount = 0;

          // Check if non-category filters are applied (same logic as above)
          const hasNonCategoryFilters = (tempFilters['rentang harga']?.length || 0) > 0 ||
                                       (tempFilters.rating?.length || 0) > 0 ||
                                       (tempFilters.pengiriman?.length || 0) > 0 ||
                                       (tempFilters.fitur?.length || 0) > 0;

          // Add all subcategories with real counts
          elektronikSubcategories.forEach((subName) => {
            // SMART LOGIC: Use filtered products ONLY if non-category filters are applied
            const productsToCount = hasNonCategoryFilters ? results : searchResults;
            const subcategoryCount = productsToCount.filter((product: any) => {
              const subNameLower = subName.toLowerCase();
              const productSubcategory = product.subcategory?.toLowerCase() || '';

              // FIXED: Use subcategory field for exact matching
              return productSubcategory === subNameLower;
            }).length;

            dynamicCategories[subName] = subcategoryCount;
            totalElektronikCount += subcategoryCount;
          });

          // Set main category count as sum of all subcategories
          dynamicCategories["Elektronik"] = totalElektronikCount;

          categories = dynamicCategories as typeof categories;
        }
      }

      // FIXED: Calculate real data for all facets based on displayed products
      const realFacets = {
        priceRanges: {
          "Di bawah Rp 100.000": 0,
          "Rp 100.000 - Rp 500.000": 0,
          "Rp 500.000 - Rp 1.000.000": 0,
          "Rp 1.000.000 - Rp 5.000.000": 0,
          "Di atas Rp 5.000.000": 0
        },
        ratings: {
          "5 Bintang": 0,
          "4 Bintang ke atas": 0,
          "3 Bintang ke atas": 0
        },
        shipping: {
          "Gratis Ongkir": 0,
          "Same Day": 0,
          "Next Day": 0
        },
        features: {
          "COD": 0,
          "SellZio Mall": 0,
          "Flash Sale": 0
        },
        provinces: {} as Record<string, number>
      };

      // Calculate real counts from displayed products (for price, rating, shipping, features, cities)
      // Note: results parameter here is already displayedProducts from useEffect
      results.forEach(product => {
        // Price ranges
        const price = parseFloat(product.price?.replace(/[^\d]/g, '') || '0')
        if (price < 100000) {
          realFacets.priceRanges["Di bawah Rp 100.000"]++
        } else if (price < 500000) {
          realFacets.priceRanges["Rp 100.000 - Rp 500.000"]++
        } else if (price < 1000000) {
          realFacets.priceRanges["Rp 500.000 - Rp 1.000.000"]++
        } else if (price < 5000000) {
          realFacets.priceRanges["Rp 1.000.000 - Rp 5.000.000"]++
        } else {
          realFacets.priceRanges["Di atas Rp 5.000.000"]++
        }

        // Ratings
        const rating = product.rating || 0
        if (rating >= 5) realFacets.ratings["5 Bintang"]++
        if (rating >= 4) realFacets.ratings["4 Bintang ke atas"]++
        if (rating >= 3) realFacets.ratings["3 Bintang ke atas"]++

        // Shipping
        if (product.shipping === "Gratis Ongkir") realFacets.shipping["Gratis Ongkir"]++
        if (product.shipping === "Same Day") realFacets.shipping["Same Day"]++
        if (product.shipping === "Next Day") realFacets.shipping["Next Day"]++

        // Features
        if (product.cod === true) realFacets.features["COD"]++
        if (product.isMall === true) realFacets.features["SellZio Mall"]++
        if (product.flashSale === true) realFacets.features["Flash Sale"]++

        // Provinces
        if (product.address && product.address.province) {
          const provinceName = product.address.province as string;
          realFacets.provinces[provinceName] = (realFacets.provinces[provinceName] || 0) + 1
        }
      });



      return {
        categories,
        priceRanges: realFacets.priceRanges,
        ratings: realFacets.ratings,
        shipping: realFacets.shipping,
        features: realFacets.features,
        provinces: realFacets.provinces
      }
    }

    const facets: FacetData = {
      categories: {},
      priceRanges: {
        "Di bawah Rp 100.000": 0,
        "Rp 100.000 - Rp 500.000": 0,
        "Rp 500.000 - Rp 1.000.000": 0,
        "Rp 1.000.000 - Rp 5.000.000": 0,
        "Di atas Rp 5.000.000": 0
      },
      ratings: {
        "5 Bintang": 0,
        "4 Bintang ke atas": 0,
        "3 Bintang ke atas": 0
      },
      shipping: {
        "Gratis Ongkir": 0,
        "Same Day": 0,
        "Next Day": 0
      },
      features: {
        "COD": 0,
        "SellZio Mall": 0,
        "Flash Sale": 0
      },
      provinces: {}
    }

    // FIXED: Only process for regular search (not subcategory context) to avoid double counting
    if (!context || !context.allSubcategories) {
      results.forEach(product => {
        // Categories - use new structure with category and subcategory
        if (product.category && product.subcategory) {
          // Count main category
          facets.categories[product.category] = (facets.categories[product.category] || 0) + 1;
          // Count subcategory
          facets.categories[product.subcategory] = (facets.categories[product.subcategory] || 0) + 1;
        }

        // Price ranges
        const price = parseFloat(product.price?.replace(/[^\d]/g, '') || '0')
        if (price < 100000) {
          facets.priceRanges["Di bawah Rp 100.000"]++
        } else if (price < 500000) {
          facets.priceRanges["Rp 100.000 - Rp 500.000"]++
        } else if (price < 1000000) {
          facets.priceRanges["Rp 500.000 - Rp 1.000.000"]++
        } else if (price < 5000000) {
          facets.priceRanges["Rp 1.000.000 - Rp 5.000.000"]++
        } else {
          facets.priceRanges["Di atas Rp 5.000.000"]++
        }

        // Ratings
        const rating = product.rating || 0
        if (rating >= 5) facets.ratings["5 Bintang"]++
        if (rating >= 4) facets.ratings["4 Bintang ke atas"]++
        if (rating >= 3) facets.ratings["3 Bintang ke atas"]++

        // Shipping
        if (product.shipping === "Gratis Ongkir") facets.shipping["Gratis Ongkir"]++
        if (product.shipping === "Same Day") facets.shipping["Same Day"]++
        if (product.shipping === "Next Day") facets.shipping["Next Day"]++

        // Features
        if (product.cod === true) facets.features["COD"]++
        if (product.isMall === true) facets.features["SellZio Mall"]++
        if (product.flashSale === true) facets.features["Flash Sale"]++

        // Provinces
        if (product.address && product.address.province) {
          const provinceName = product.address.province as string;
          facets.provinces[provinceName] = (facets.provinces[provinceName] || 0) + 1
        }
      })
    }

    // Categories are already handled in the first system above when we have subcategory context
    // No need to add missing subcategories here as they're already calculated with real counts

    return facets
  }

  const handleFilterChange = (type: keyof ActiveFilters, value: string, checked: boolean) => {
    setTempFilters(prev => {
      const newFilters = { ...prev }
      if (!newFilters[type]) newFilters[type] = []

      const context = subcategoryContext || (window as any).subcategoryContext;
      const isKategoriType = type === 'kategori';

      if (checked) {
        if (!newFilters[type]!.includes(value)) {
          newFilters[type]!.push(value)
        }

        // Auto-check main category when subcategory is selected
        if (isKategoriType && context && context.allSubcategories) {
          const isSubcategory = context.allSubcategories.some((sub: any) => sub.name === value);
          if (isSubcategory) {
            // Auto-check main category when subcategory is selected
            if (!newFilters[type]!.includes(context.category)) {
              newFilters[type]!.push(context.category);
            }
          }
        }
      } else {
        // FIXED: Prevent unchecking main category when subcategories are still selected
        if (isKategoriType && context && context.allSubcategories) {
          const isMainCategory = value === context.category;
          const hasSelectedSubcategories = context.allSubcategories.some((sub: any) =>
            newFilters[type]?.includes(sub.name)
          );

          // Don't allow unchecking main category if subcategories are selected
          if (isMainCategory && hasSelectedSubcategories) {
            return prev; // Return previous state without changes
          }
        }

        // FIXED: When unchecking subcategory, ensure main category stays checked if in subcategory context
        if (isKategoriType && context && context.allSubcategories) {
          const isSubcategory = context.allSubcategories.some((sub: any) => sub.name === value);
          if (isSubcategory) {
            // After removing this subcategory, ensure main category stays checked in subcategory context
            const updatedFilters = newFilters[type]!.filter(item => item !== value);

            // If we're in subcategory context, always keep main category checked
            if (!updatedFilters.includes(context.category)) {
              updatedFilters.push(context.category);
            }

            newFilters[type] = updatedFilters;
            if (newFilters[type]!.length === 0) {
              delete newFilters[type];
            }
            return newFilters;
          }
        }

        newFilters[type] = newFilters[type]!.filter(item => item !== value)

        if (newFilters[type]!.length === 0) {
          delete newFilters[type]
        }
      }

      // FIXED: Auto-apply filters for desktop sidebar immediately
      if (isDesktopSidebar) {
        // Apply filters immediately for desktop sidebar
        setTimeout(() => {
          onFiltersChange(newFilters)
        }, 0)
      }

      return newFilters
    })
  }



  const applyFilters = () => {
    onFiltersChange(tempFilters)
    // Don't close if it's desktop sidebar
    if (!isDesktopSidebar) {
      onClose()
    }
  }

  const resetFilters = () => {
    setTempFilters({})
    onFiltersChange({})
    // Don't close if it's desktop sidebar
    if (!isDesktopSidebar) {
      onClose()
    }
  }

  const countActiveFilters = () => {
    return Object.values(tempFilters).reduce((total, values) => total + (values?.length || 0), 0)
  }

  const toggleProvinceExpansion = (provinceName: string) => {
    setExpandedProvinces(prev => ({
      ...prev,
      [provinceName]: !prev[provinceName]
    }))
  }

  const getCitiesForProvince = (provinceName: string) => {
    // Get all products for this province and extract unique cities from ALL products, not just filtered results
    const provinceCities: Record<string, number> = {}

    // Use all products to get accurate city counts for the province
    const allProductsToCheck = allProducts.length > 0 ? allProducts : (searchResults || [])

    allProductsToCheck.forEach((product: any) => {
      if (product.address?.province === provinceName && product.address?.city) {
        const cityName = product.address.city
        provinceCities[cityName] = (provinceCities[cityName] || 0) + 1
      }
    })

    return provinceCities
  }

  const renderFacetSection = (title: string, items: Record<string, number>, type: keyof ActiveFilters) => {
    const hasItems = Object.keys(items).some(key => items[key] > 0)
    if (!hasItems) return null

    // Check if this is kategori section
    const context = subcategoryContext || (window as any).subcategoryContext;
    const isKategoriSection = type === 'kategori';

    // ENHANCED: Create effective context for hierarchical display
    let effectiveContext = context;
    if (!context && isKategoriSection && searchResults.length > 0) {
      const detectedCategory = detectMainCategoryFromResults(searchResults);
      if (detectedCategory) {
        effectiveContext = {
          category: detectedCategory.name,
          allSubcategories: detectedCategory.subcategories,
          selectedSubcategory: null
        };
      }
    }

    // FIXED: Always show hierarchical structure for categories when we have effective context
    const shouldShowHierarchical = isKategoriSection && effectiveContext && effectiveContext.allSubcategories;

    return (
      <div className="facet-section">
        <h3>{title}</h3>
        <ul>
          {shouldShowHierarchical ? (
            // Render category name first, then subcategories with indentation
            <>
              {/* Category name with checkbox - calculate total count */}
              {(() => {
                // Safety check for effective context
                if (!effectiveContext || !effectiveContext.category) {
                  return null;
                }

                const categoryCount = items[effectiveContext.category] || 0;
                // Check if any subcategory is selected
                const hasSelectedSubcategories = effectiveContext.allSubcategories?.some((sub: any) =>
                  tempFilters[type]?.includes(sub.name)
                ) || false;
                // FIXED: Auto-check main category when subcategory is selected, keep checked even when all subcategories unchecked
                const isCategoryChecked = tempFilters[type]?.includes(effectiveContext.category) || hasSelectedSubcategories;
                const categoryCheckboxId = `facet-${type}-${effectiveContext.category.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`;

                return (
                  <li>
                    <input
                      type="checkbox"
                      id={categoryCheckboxId}
                      className="orange-checkbox"
                      checked={isCategoryChecked}
                      disabled={true} // Always disable main category in subcategory context
                      onChange={(e) => {
                        // Prevent any changes to main category when in subcategory context
                        e.preventDefault();
                      }}
                      data-facet-type={type}
                      data-facet-value={effectiveContext.category}
                    />
                    <label htmlFor={categoryCheckboxId} className={`category-name ${hasSelectedSubcategories ? 'disabled' : ''}`}>
                      {effectiveContext.category} ({categoryCount})
                    </label>
                  </li>
                );
              })()}

              {/* Subcategories with indentation and checkboxes */}
              {(() => {
                // Safety check for effective context
                if (!effectiveContext || !effectiveContext.category) {
                  return null;
                }

                // Get subcategories in sorted order (selected first)
                const subcategoryEntries = Object.entries(items).filter(([key]) => key !== effectiveContext.category);

                // Use sorted order if available, otherwise use original order
                let orderedSubcategories = subcategoryEntries;
                if (sortedSubcategories.length > 0) {
                  orderedSubcategories = sortedSubcategories
                    .map(name => subcategoryEntries.find(([key]) => key === name))
                    .filter(Boolean) as [string, number][];

                  // Add any missing subcategories at the end
                  const missingSubcategories = subcategoryEntries.filter(([key]) =>
                    !sortedSubcategories.includes(key)
                  );
                  orderedSubcategories = [...orderedSubcategories, ...missingSubcategories];
                }

                // SMART SORTING: Move disabled/zero count subcategories to bottom
                const enabledSubcategories = orderedSubcategories.filter(([_, count]) => count > 0);
                const disabledSubcategories = orderedSubcategories.filter(([_, count]) => count === 0);

                // Final order: enabled first, then disabled at bottom
                orderedSubcategories = [...enabledSubcategories, ...disabledSubcategories];

                // Show only 5 items initially with expand button for all devices
                const maxInitialItems = 5;
                const shouldShowExpandButton = orderedSubcategories.length > maxInitialItems;
                const itemsToShow = shouldShowExpandButton && !isSubcategoriesExpanded
                  ? orderedSubcategories.slice(0, maxInitialItems)
                  : orderedSubcategories;

                return (
                  <>
                    {itemsToShow.map(([key, count]) => {
                      const isChecked = tempFilters[type]?.includes(key) || false
                      const checkboxId = `facet-${type}-${key.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`

                      // FIXED DISABLED LOGIC: Disable subcategory when no products after filtering
                      let isDisabled = count === 0; // Default: disabled if no products

                      // ENHANCED LOGIC: When non-category filters are applied, disable subcategories with 0 count
                      // but keep them checked if they were previously selected
                      const hasNonCategoryFilters = (tempFilters['rentang harga']?.length || 0) > 0 ||
                                                   (tempFilters.rating?.length || 0) > 0 ||
                                                   (tempFilters.pengiriman?.length || 0) > 0 ||
                                                   (tempFilters.fitur?.length || 0) > 0;

                      if (hasNonCategoryFilters && effectiveContext && effectiveContext.allSubcategories) {
                        // If this subcategory has no products after filtering, disable it but keep it checked
                        if (count === 0 && isChecked) {
                          isDisabled = true;
                        }

                        // FIXED: Remove auto-disable logic that prevents unchecking
                        // Allow users to uncheck even if it's the only subcategory
                      }

                      return (
                        <li key={key} className="subcategory-item">
                          <input
                            type="checkbox"
                            id={checkboxId}
                            className="orange-checkbox"
                            checked={isChecked}
                            onChange={(e) => handleFilterChange(type, key, e.target.checked)}
                            data-facet-type={type}
                            data-facet-value={key}
                            disabled={isDisabled}
                          />
                          <label htmlFor={checkboxId} className={isDisabled ? 'disabled' : ''}>
                            {key} ({count})
                          </label>
                        </li>
                      )
                    })}

                    {/* Expand/Collapse button for all devices */}
                    {shouldShowExpandButton && (
                      <li className="subcategory-expand-button">
                        <button
                          className="expand-button"
                          onClick={() => setIsSubcategoriesExpanded(!isSubcategoriesExpanded)}
                        >
                          {isSubcategoriesExpanded ? (
                            <>
                              <span>Tampilkan Lebih Sedikit</span>
                              <span className="expand-arrow up">▲</span>
                            </>
                          ) : (
                            <>
                              <span>Tampilkan {orderedSubcategories.length - maxInitialItems} Lainnya</span>
                              <span className="expand-arrow down">▼</span>
                            </>
                          )}
                        </button>
                      </li>
                    )}
                  </>
                );
              })()}
            </>
          ) : (
            // Regular rendering for other sections with smart sorting
            (() => {
              // SMART SORTING: Move disabled/zero count items to bottom for all sections
              const allEntries = Object.entries(items);
              const enabledEntries = allEntries.filter(([_, count]) => count > 0);
              const disabledEntries = allEntries.filter(([_, count]) => count === 0);
              const sortedEntries = [...enabledEntries, ...disabledEntries];

              // Special handling for provinces - show only 5 initially with expand button and city dropdown
              const isProvinceSection = type === 'provinsi';
              const maxInitialItems = 5;
              const shouldShowExpandButton = isProvinceSection && sortedEntries.length > maxInitialItems;
              const itemsToShow = shouldShowExpandButton && !isCitiesExpanded
                ? sortedEntries.slice(0, maxInitialItems)
                : sortedEntries;

              return (
                <>
                  {itemsToShow.map(([key, count]) => {
                    // Show all items regardless of count
                    const isChecked = tempFilters[type]?.includes(key) || false
                    const checkboxId = `facet-${type}-${key.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`

                    // Special rendering for provinces with city dropdown
                    if (isProvinceSection) {
                      const isExpanded = expandedProvinces[key] || false
                      const cities = getCitiesForProvince(key)
                      const hasCities = Object.keys(cities).length > 0

                      return (
                        <li key={key} className="province-item">
                          <div className="province-header">
                            <input
                              type="checkbox"
                              id={checkboxId}
                              className="orange-checkbox"
                              checked={isChecked}
                              onChange={(e) => handleFilterChange(type, key, e.target.checked)}
                              data-facet-type={type}
                              data-facet-value={key}
                              disabled={count === 0}
                            />
                            <label htmlFor={checkboxId}>
                              {key} ({count})
                            </label>
                            {hasCities && (
                              <button
                                className="city-dropdown-toggle"
                                onClick={() => toggleProvinceExpansion(key)}
                                type="button"
                              >
                                {isExpanded ? '▲' : '▼'}
                              </button>
                            )}
                          </div>

                          {/* City dropdown */}
                          {isExpanded && hasCities && (
                            <ul className="city-dropdown">
                              {Object.entries(cities).map(([cityName, cityCount]) => {
                                const isCityChecked = tempFilters.kota?.includes(cityName) || false
                                const cityCheckboxId = `facet-kota-${cityName.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`

                                return (
                                  <li key={cityName} className="city-item">
                                    <input
                                      type="checkbox"
                                      id={cityCheckboxId}
                                      className="orange-checkbox"
                                      checked={isCityChecked}
                                      onChange={(e) => handleFilterChange('kota', cityName, e.target.checked)}
                                      data-facet-type="kota"
                                      data-facet-value={cityName}
                                      disabled={cityCount === 0}
                                    />
                                    <label htmlFor={cityCheckboxId}>
                                      {cityName} ({cityCount})
                                    </label>
                                  </li>
                                )
                              })}
                            </ul>
                          )}
                        </li>
                      )
                    }

                    // Regular rendering for non-province sections
                    return (
                      <li key={key}>
                        <input
                          type="checkbox"
                          id={checkboxId}
                          className="orange-checkbox"
                          checked={isChecked}
                          onChange={(e) => handleFilterChange(type, key, e.target.checked)}
                          data-facet-type={type}
                          data-facet-value={key}
                          disabled={count === 0}
                        />
                        <label htmlFor={checkboxId}>
                          {key} ({count})
                        </label>
                      </li>
                    )
                  })}

                  {/* Expand/Collapse button for provinces */}
                  {shouldShowExpandButton && (
                    <li className="province-expand-button">
                      <button
                        className="expand-button"
                        onClick={() => setIsCitiesExpanded(!isCitiesExpanded)}
                      >
                        {isCitiesExpanded ? (
                          <>
                            <span>Tampilkan Lebih Sedikit</span>
                            <span className="expand-arrow up">▲</span>
                          </>
                        ) : (
                          <>
                            <span>Lihat {sortedEntries.length - maxInitialItems} Lainnya</span>
                            <span className="expand-arrow down">▼</span>
                          </>
                        )}
                      </button>
                    </li>
                  )}
                </>
              );
            })()
          )}
        </ul>
      </div>
    )
  }



  // For desktop sidebar, always show when isDesktopSidebar is true
  // For mobile/tablet popup, only show when isVisible is true
  if (!isDesktopSidebar && !isVisible) return null

  // Desktop Sidebar Layout
  if (isDesktopSidebar) {
    return (
      <div className="facet-sidebar-desktop">
        <div className="facet-header">
          <div className="facet-title">
            <Filter size={18} className="facet-filter-icon" />
            Filter
          </div>
        </div>

        <div className="facet-content-wrapper">
          <div className="facet-content">
            {renderFacetSection('Kategori', facetData.categories, 'kategori')}
            {renderFacetSection('Provinsi', facetData.provinces, 'provinsi')}
            {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
            {renderFacetSection('Rating', facetData.ratings, 'rating')}
            {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
            {renderFacetSection('Fitur', facetData.features, 'fitur')}
          </div>
        </div>

        <div className="facet-buttons">
          <div className="facet-button facet-button-reset" onClick={resetFilters}>
            Reset
          </div>
          <div className="facet-button facet-button-apply" onClick={applyFilters}>
            Terapkan ({countActiveFilters()})
          </div>
        </div>
      </div>
    )
  }

  // Mobile/Tablet Popup Layout
  return (
    <>
      {/* Mobile/Tablet Overlay */}
      <div className="facet-overlay" style={{ display: (isMobile || isTablet) ? 'flex' : 'none' }}>
        <div className="facet-panel">
          <div className="facet-header">
            <div className="facet-title">Filter</div>
            <div className="facet-close" onClick={onClose}>
              <X size={18} />
            </div>
          </div>

          <div className="facet-content-wrapper">
            <div className="facet-content">
              {renderFacetSection('Kategori', facetData.categories, 'kategori')}
              {renderFacetSection('Provinsi', facetData.provinces, 'provinsi')}
              {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
              {renderFacetSection('Rating', facetData.ratings, 'rating')}
              {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
              {renderFacetSection('Fitur', facetData.features, 'fitur')}
            </div>
          </div>

          <div className="facet-buttons">
            <div className="facet-button facet-button-reset" onClick={resetFilters}>
              Reset
            </div>
            <div className="facet-button facet-button-apply" onClick={applyFilters}>
              Terapkan ({countActiveFilters()})
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Panel - Only for non-sidebar mode */}
      <div
        className="facet-panel-desktop"
        style={{ display: (!isMobile && !isTablet) ? 'block' : 'none' }}
      >
        <div className="facet-header">
          <div className="facet-title">Filter</div>
          <div className="facet-close" onClick={onClose}>
            <X size={18} />
          </div>
        </div>

        <div className="facet-content-wrapper">
          <div className="facet-content">
            {renderFacetSection('Kategori', facetData.categories, 'kategori')}
            {renderFacetSection('Provinsi', facetData.provinces, 'provinsi')}
            {renderFacetSection('Rentang Harga', facetData.priceRanges, 'rentang harga')}
            {renderFacetSection('Rating', facetData.ratings, 'rating')}
            {renderFacetSection('Pengiriman', facetData.shipping, 'pengiriman')}
            {renderFacetSection('Fitur', facetData.features, 'fitur')}
          </div>
        </div>

        <div className="facet-buttons">
          <div className="facet-button facet-button-reset" onClick={resetFilters}>
            Reset
          </div>
          <div className="facet-button facet-button-apply" onClick={applyFilters}>
            Terapkan ({countActiveFilters()})
          </div>
        </div>
      </div>
    </>
  )
}